TOOLS_LIST = {"codebase": {
    "type": "function",
    "function": {
        "name": "context_search",
        "description": "Search for relevant information within a specific knowledge-base using semantic search.",
        "parameters": {
            "type": "object",
            "properties": {
                "query": {
                    "type": "string",
                    "description": "The search query to find relevant information"
                },
                "kbid": {
                    "type": "string",
                    "description": "The knowledge-base ID to search within"
                }
            },
            "required": ["query", "kbid"],
        }
    }
},
"folder": {
    "type": "function",
    "function": {
        "name": "folder_search",
        "description": "Search for relevant information within a specific folder using semantic search within a knowledge-base.",
        "parameters": {
            "type": "object",
            "properties": {
                "query": {
                    "type": "string",
                    "description": "The search query to find relevant information"
                },
                "kbid": {
                    "type": "string",
                    "description": "The knowledge-base ID to search within"
                },
                "folder_path": {
                    "type": "string",
                    "description": "The folder to search within"
                }
            },
            "required": ["query", "kbid", "folder_path"],
        }
    }
},
"websearch": {
    "type": "function",
    "function": {
        "name": "web_search",
        "description": "Search the web for relevant information and external resources.",
        "parameters": {
            "type": "object",
            "properties": {
                "query": {
                    "type": "string",
                    "description": "The search query to find relevant information on the web"
                }
            },
            "required": ["query"]
        }
    }
},
"swagger": {
    "type": "function",
    "function": {
        "name": "swagger_search",
        "description": "Search API documentation and endpoints using Swagger/OpenAPI specifications",
        "parameters": {
            "type": "object",
            "properties": {
                "query": {
                    "type": "string",
                    "description": "The search query to find relevant API endpoints"
                },
                "kbid": {
                    "type": "string",
                    "description": "The knowledge base ID containing the Swagger documentation"
                }
            },
            "required": ["query", "kbid"]
        }
    }
}}



def get(contexts: list, is_web_search: bool = False) -> dict:
    """
    RETURN THE TOOLS LIST BASED ON THE CONTEXTS PROVIDED
    """
    print("Getting tools list for contexts:", contexts)
    
    if not contexts or len(contexts) == 0:
        return []

    tools_list = []
    for context in contexts:
        tools_list.append(TOOLS_LIST.get(context["type"]))

    if is_web_search:
        tools_list.append(TOOLS_LIST.get("websearch"))

    # De-dup tools_list - can't use set() with dicts, so use manual deduplication
    seen_tools = []
    unique_tools = []
    for tool in tools_list:
        if tool is not None and tool not in seen_tools:
            seen_tools.append(tool)
            unique_tools.append(tool)

    print("Returning tools list:", unique_tools)
    return unique_tools